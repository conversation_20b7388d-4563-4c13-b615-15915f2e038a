package com.vgop.service.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 告警信息实体类
 * 对应数据库表：vgop_validation_alerts
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ValidationAlert {
    
    /**
     * 告警ID
     */
    private Long alertId;
    
    /**
     * 告警时间（格式：YYYYMMDDHHMMSS）
     */
    private String alertTime;
    
    /**
     * 接口名称
     */
    private String interfaceName;
    
    /**
     * 告警类型
     */
    private String alertType;
    
    /**
     * 告警级别
     */
    private AlertLevel alertLevel;
    
    /**
     * 告警信息
     */
    private String alertMessage;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 行号
     */
    private Long lineNumber;
    
    /**
     * 错误数据
     */
    private String errorData;
    
    /**
     * 字段错误详情
     */
    private String fieldErrors;
    
    /**
     * 指标详情
     */
    private String metricDetails;
    
    /**
     * Excel报告路径
     */
    private String excelReportPath;
    
    /**
     * 状态
     */
    private AlertStatus status;
    
    /**
     * 处理人
     */
    private String handledBy;
    
    /**
     * 处理时间（格式：YYYYMMDDHHMMSS）
     */
    private String handledTime;

    /**
     * 批次ID（用于区分不同批次的数据质量告警记录）
     */
    private String batchId;

    // 日期时间格式化器
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    
    /**
     * 获取当前时间字符串
     * @return YYYYMMDDHHMMSS格式的当前时间
     */
    public static String getCurrentTimeString() {
        return LocalDateTime.now().format(DATETIME_FORMATTER);
    }
    
    /**
     * 设置当前时间为告警时间
     */
    public void setCurrentAlertTime() {
        this.alertTime = getCurrentTimeString();
    }
    
    /**
     * 设置当前时间为处理时间
     */
    public void setCurrentHandledTime() {
        this.handledTime = getCurrentTimeString();
    }
    
    /**
     * 告警级别枚举
     */
    public enum AlertLevel {
        INFO("信息"),
        WARNING("警告"),
        ERROR("错误"),
        CRITICAL("严重");
        
        private final String description;
        
        AlertLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 告警状态枚举
     */
    public enum AlertStatus {
        NEW("新建"),
        ACKNOWLEDGED("已确认"),
        PROCESSING("处理中"),
        RESOLVED("已解决"),
        CLOSED("已关闭");
        
        private final String description;
        
        AlertStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
} 